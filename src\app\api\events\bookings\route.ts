import { NextRequest } from 'next/server'

/**
 * Server-Sent Events endpoint for real-time booking updates
 * 
 * This endpoint provides a persistent connection for real-time updates
 * about booking events (new bookings, cancellations, updates)
 */

// Store active connections
const connections = new Set<WritableStreamDefaultWriter>()

// Simulated event store (in production, use Redis or similar)
let eventCounter = 0

export async function GET(request: NextRequest) {
  // Set up Server-Sent Events headers
  const responseHeaders = new Headers({
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Cache-Control',
  })

  // Create a readable stream for SSE
  const stream = new ReadableStream({
    start(controller) {
      const encoder = new TextEncoder()
      
      // Send initial connection message
      const initialMessage = {
        id: ++eventCounter,
        type: 'connection',
        data: {
          message: 'Connected to real-time booking updates',
          timestamp: new Date().toISOString()
        }
      }
      
      controller.enqueue(encoder.encode(`data: ${JSON.stringify(initialMessage)}\n\n`))
      
      // Store the controller for broadcasting
      const writer = controller as any
      connections.add(writer)
      
      // Send heartbeat every 30 seconds to keep connection alive
      const heartbeatInterval = setInterval(() => {
        try {
          const heartbeat = `data: ${JSON.stringify({
            type: 'heartbeat',
            timestamp: new Date().toISOString()
          })}\n\n`
          
          controller.enqueue(encoder.encode(heartbeat))
        } catch (error) {
          console.error('Heartbeat failed:', error)
          clearInterval(heartbeatInterval)
          connections.delete(writer)
        }
      }, 30000)
      
      // Handle connection close
      request.signal.addEventListener('abort', () => {
        console.log('SSE connection closed')
        clearInterval(heartbeatInterval)
        connections.delete(writer)
        try {
          controller.close()
        } catch (error) {
          // Connection already closed
        }
      })
    }
  })

  return new Response(stream, {
    headers: responseHeaders,
  })
}

/**
 * Broadcast booking event to all connected clients
 */
export function broadcastBookingEvent(eventType: string, bookingData: any) {
  const event = {
    id: ++eventCounter,
    type: eventType,
    data: bookingData,
    timestamp: new Date().toISOString()
  }

  const message = `data: ${JSON.stringify(event)}\n\n`
  const encoder = new TextEncoder()
  const encodedMessage = encoder.encode(message)

  // Send to all connected clients
  connections.forEach((writer) => {
    try {
      writer.enqueue(encodedMessage)
    } catch (error) {
      console.error('Failed to send to client:', error)
      connections.delete(writer)
    }
  })

  console.log(`📡 Broadcasted ${eventType} to ${connections.size} clients`)
}

/**
 * Get connection statistics
 */
export function getConnectionStats() {
  return {
    activeConnections: connections.size,
    totalEvents: eventCounter
  }
}

// Handle preflight requests for CORS
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Cache-Control',
    },
  })
}
