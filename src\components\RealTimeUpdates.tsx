'use client'

import { useEffect, useState } from 'react'
import { useNotifications } from '@/lib/notifications'

interface BookingUpdate {
  id: string
  type: 'new_booking' | 'booking_cancelled' | 'booking_updated'
  data: {
    bookingId: string
    clientName: string
    clientEmail: string
    eventType: string
    scheduledTime: string
    source: string
  }
  timestamp: string
}

interface RealTimeUpdatesProps {
  onUpdate?: (update: BookingUpdate) => void
  showToasts?: boolean
}

export default function RealTimeUpdates({ onUpdate, showToasts = true }: RealTimeUpdatesProps) {
  const [isConnected, setIsConnected] = useState(false)
  const [lastUpdate, setLastUpdate] = useState<BookingUpdate | null>(null)
  const [updateCount, setUpdateCount] = useState(0)
  const { notifyNewBooking, notifyBookingCancelled, notifySystem } = useNotifications()

  useEffect(() => {
    // Check if EventSource is supported
    if (!window.EventSource) {
      console.warn('Server-Sent Events not supported')
      return
    }

    // Create EventSource connection
    const eventSource = new EventSource('/api/events/bookings')

    eventSource.onopen = () => {
      console.log('✅ Real-time connection established')
      setIsConnected(true)
      if (showToasts) {
        notifySystem('Connected', 'Real-time updates enabled', 'success')
      }
    }

    eventSource.onmessage = (event) => {
      try {
        const update: BookingUpdate = JSON.parse(event.data)
        console.log('📡 Real-time update received:', update)
        
        setLastUpdate(update)
        setUpdateCount(prev => prev + 1)
        
        // Call update handler
        if (onUpdate) {
          onUpdate(update)
        }

        // Show notifications based on update type
        if (showToasts) {
          handleUpdateNotification(update)
        }

      } catch (error) {
        console.error('Error parsing real-time update:', error)
      }
    }

    eventSource.onerror = (error) => {
      console.error('❌ Real-time connection error:', error)
      setIsConnected(false)
      
      if (eventSource.readyState === EventSource.CLOSED) {
        console.log('Connection closed, attempting to reconnect...')
        // Reconnection will be handled by browser automatically
      }
    }

    // Cleanup on unmount
    return () => {
      eventSource.close()
      setIsConnected(false)
    }
  }, [onUpdate, showToasts, notifyNewBooking, notifyBookingCancelled, notifySystem])

  const handleUpdateNotification = async (update: BookingUpdate) => {
    switch (update.type) {
      case 'new_booking':
        await notifyNewBooking({
          bookingId: update.data.bookingId,
          clientName: update.data.clientName,
          clientEmail: update.data.clientEmail,
          eventType: update.data.eventType,
          scheduledTime: update.data.scheduledTime,
          source: update.data.source as any
        })
        break

      case 'booking_cancelled':
        await notifyBookingCancelled({
          bookingId: update.data.bookingId,
          clientName: update.data.clientName,
          clientEmail: update.data.clientEmail,
          eventType: update.data.eventType,
          scheduledTime: update.data.scheduledTime,
          source: update.data.source as any
        })
        break

      case 'booking_updated':
        await notifySystem(
          'Booking Updated',
          `${update.data.clientName}'s ${update.data.eventType} was updated`,
          'info'
        )
        break
    }
  }

  // Don't render anything visible by default
  return (
    <div className="real-time-updates" style={{ display: 'none' }}>
      {/* Hidden component for real-time functionality */}
      <div className="connection-status" data-connected={isConnected}>
        {isConnected ? 'Connected' : 'Disconnected'}
      </div>
      <div className="update-count" data-count={updateCount}>
        Updates: {updateCount}
      </div>
      {lastUpdate && (
        <div className="last-update" data-type={lastUpdate.type}>
          Last: {lastUpdate.type} at {new Date(lastUpdate.timestamp).toLocaleTimeString()}
        </div>
      )}
    </div>
  )
}

/**
 * Hook for real-time booking updates
 */
export function useRealTimeBookings() {
  const [updates, setUpdates] = useState<BookingUpdate[]>([])
  const [isConnected, setIsConnected] = useState(false)

  useEffect(() => {
    if (!window.EventSource) {
      return
    }

    const eventSource = new EventSource('/api/events/bookings')

    eventSource.onopen = () => {
      setIsConnected(true)
    }

    eventSource.onmessage = (event) => {
      try {
        const update: BookingUpdate = JSON.parse(event.data)
        setUpdates(prev => [update, ...prev.slice(0, 49)]) // Keep last 50 updates
      } catch (error) {
        console.error('Error parsing update:', error)
      }
    }

    eventSource.onerror = () => {
      setIsConnected(false)
    }

    return () => {
      eventSource.close()
      setIsConnected(false)
    }
  }, [])

  return {
    updates,
    isConnected,
    latestUpdate: updates[0] || null
  }
}

/**
 * Real-time status indicator component
 */
export function RealTimeStatus() {
  const [isConnected, setIsConnected] = useState(false)
  const [updateCount, setUpdateCount] = useState(0)

  useEffect(() => {
    if (!window.EventSource) {
      return
    }

    const eventSource = new EventSource('/api/events/bookings')

    eventSource.onopen = () => {
      setIsConnected(true)
    }

    eventSource.onmessage = () => {
      setUpdateCount(prev => prev + 1)
    }

    eventSource.onerror = () => {
      setIsConnected(false)
    }

    return () => {
      eventSource.close()
    }
  }, [])

  return (
    <div className="real-time-status">
      <div className={`status-indicator ${isConnected ? 'connected' : 'disconnected'}`}>
        <div className="status-dot"></div>
        <span>{isConnected ? 'Live' : 'Offline'}</span>
      </div>
      {updateCount > 0 && (
        <div className="update-badge">
          {updateCount}
        </div>
      )}
    </div>
  )
}

// CSS styles for the status indicator
const statusStyles = `
.real-time-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ef4444;
  animation: pulse 2s infinite;
}

.status-indicator.connected .status-dot {
  background: #10b981;
}

.update-badge {
  background: var(--accent-color);
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: 600;
  min-width: 16px;
  text-align: center;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}
`

// Inject styles
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style')
  styleSheet.textContent = statusStyles
  document.head.appendChild(styleSheet)
}
