import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/supabase'
import { broadcastBookingEvent } from '@/app/api/events/bookings/route'

// API route for handling booking data from Calendly events
// This works for both free plan (client-side events) and paid plan (webhooks)
export async function POST(request: NextRequest) {
  try {
    const bookingData = await request.json()
    
    // Validate required fields
    if (!bookingData.invitee_email) {
      return NextResponse.json(
        { error: 'Missing required field: invitee_email' },
        { status: 400 }
      )
    }

    // Prepare booking data for Supabase
    const booking = {
      calendly_id: bookingData.event_uri || `manual-${Date.now()}`,
      event_type: bookingData.event_type || 'consultation',
      event_name: bookingData.event_name || '30-minute consultation',
      invitee_email: bookingData.invitee_email,
      invitee_name: bookingData.invitee_name || '',
      invitee_timezone: bookingData.invitee_timezone || '',
      scheduled_at: bookingData.scheduled_at || new Date().toISOString(),
      start_time: bookingData.start_time || new Date().toISOString(),
      end_time: bookingData.end_time || new Date(Date.now() + 30 * 60 * 1000).toISOString(),
      status: 'active' as const,
      meeting_url: bookingData.meeting_url || '',
      location: bookingData.location || 'Online',
      raw_data: bookingData.raw_data || bookingData
    }

    // Save to Supabase
    const result = await db.bookings.upsert(booking)

    console.log('✅ Booking saved successfully:', {
      calendly_id: booking.calendly_id,
      invitee_email: booking.invitee_email,
      event_type: booking.event_type
    })

    // Broadcast real-time event
    try {
      broadcastBookingEvent('new_booking', {
        bookingId: result.id,
        clientName: booking.invitee_name,
        clientEmail: booking.invitee_email,
        eventType: booking.event_type,
        scheduledTime: booking.start_time,
        source: booking.calendly_id.startsWith('manual-') ? 'manual' :
                booking.calendly_id.startsWith('gcal-') ? 'google_calendar' :
                booking.calendly_id.startsWith('email-') ? 'email' : 'calendly'
      })
    } catch (error) {
      console.error('Failed to broadcast booking event:', error)
    }

    return NextResponse.json(
      {
        success: true,
        message: 'Booking saved successfully',
        booking_id: result.id,
        calendly_id: booking.calendly_id
      },
      { status: 200 }
    )

  } catch (error) {
    console.error('❌ Error saving booking:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to save booking',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// Handle GET requests to retrieve bookings (for admin/analytics)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '10')
    const offset = parseInt(searchParams.get('offset') || '0')
    
    // Get recent bookings
    const bookings = await db.bookings.getAll()
    
    // Apply pagination
    const paginatedBookings = bookings.slice(offset, offset + limit)
    
    return NextResponse.json(
      {
        bookings: paginatedBookings,
        total: bookings.length,
        limit,
        offset
      },
      { status: 200 }
    )
    
  } catch (error) {
    console.error('❌ Error fetching bookings:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch bookings',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
