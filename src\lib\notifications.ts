/**
 * Real-time Notifications System
 * 
 * Provides browser notifications, real-time updates, and notification management
 * for booking events and system updates.
 */

interface NotificationOptions {
  title: string
  body: string
  icon?: string
  badge?: string
  tag?: string
  requireInteraction?: boolean
  actions?: NotificationAction[]
  data?: any
}



class NotificationManager {
  private static instance: NotificationManager
  private permission: NotificationPermission = 'default'
  private subscribers: Set<(data: any) => void> = new Set()

  private constructor() {
    this.checkPermission()
    this.setupServiceWorker()
  }

  static getInstance(): NotificationManager {
    if (!NotificationManager.instance) {
      NotificationManager.instance = new NotificationManager()
    }
    return NotificationManager.instance
  }

  /**
   * Request notification permission from user
   */
  async requestPermission(): Promise<NotificationPermission> {
    if (!('Notification' in window)) {
      console.warn('This browser does not support notifications')
      return 'denied'
    }

    if (this.permission === 'granted') {
      return 'granted'
    }

    try {
      this.permission = await Notification.requestPermission()
      return this.permission
    } catch (error) {
      console.error('Error requesting notification permission:', error)
      return 'denied'
    }
  }

  /**
   * Check current notification permission
   */
  private checkPermission(): void {
    if ('Notification' in window) {
      this.permission = Notification.permission
    }
  }

  /**
   * Setup service worker for background notifications
   */
  private async setupServiceWorker(): Promise<void> {
    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.register('/sw.js')
        console.log('Service Worker registered:', registration)
      } catch (error) {
        console.error('Service Worker registration failed:', error)
      }
    }
  }

  /**
   * Show browser notification
   */
  async showNotification(options: NotificationOptions): Promise<void> {
    if (this.permission !== 'granted') {
      const permission = await this.requestPermission()
      if (permission !== 'granted') {
        console.warn('Notification permission denied')
        return
      }
    }

    try {
      const notification = new Notification(options.title, {
        body: options.body,
        icon: options.icon || '/favicon.ico',
        badge: options.badge || '/favicon.ico',
        tag: options.tag,
        requireInteraction: options.requireInteraction || false,
        data: options.data
      })

      // Auto-close after 5 seconds unless requireInteraction is true
      if (!options.requireInteraction) {
        setTimeout(() => {
          notification.close()
        }, 5000)
      }

      // Handle notification click
      notification.onclick = (event) => {
        event.preventDefault()
        window.focus()
        
        // Handle specific actions based on notification data
        // Add custom actions here if needed
        
        notification.close()
      }

    } catch (error) {
      console.error('Error showing notification:', error)
    }
  }



  /**
   * Show system notification
   */
  async notifySystem(title: string, message: string, type: 'info' | 'success' | 'warning' | 'error' = 'info'): Promise<void> {
    const icons = {
      info: '💡',
      success: '✅',
      warning: '⚠️',
      error: '❌'
    }

    await this.showNotification({
      title: `${icons[type]} ${title}`,
      body: message,
      tag: `system-${type}-${Date.now()}`
    })
  }

  /**
   * Subscribe to real-time updates
   */
  subscribe(callback: (data: any) => void): () => void {
    this.subscribers.add(callback)
    
    // Return unsubscribe function
    return () => {
      this.subscribers.delete(callback)
    }
  }

  /**
   * Notify all subscribers
   */
  private notifySubscribers(data: any): void {
    this.subscribers.forEach(callback => {
      try {
        callback(data)
      } catch (error) {
        console.error('Error in notification subscriber:', error)
      }
    })
  }

  /**
   * Check if notifications are supported and enabled
   */
  isSupported(): boolean {
    return 'Notification' in window
  }

  /**
   * Get current permission status
   */
  getPermission(): NotificationPermission {
    return this.permission
  }
}

/**
 * React hook for notifications
 */
export function useNotifications() {
  const notificationManager = NotificationManager.getInstance()

  const requestPermission = async () => {
    return await notificationManager.requestPermission()
  }

  const showNotification = async (options: NotificationOptions) => {
    return await notificationManager.showNotification(options)
  }



  const notifySystem = async (title: string, message: string, type?: 'info' | 'success' | 'warning' | 'error') => {
    return await notificationManager.notifySystem(title, message, type)
  }

  const subscribe = (callback: (data: any) => void) => {
    return notificationManager.subscribe(callback)
  }

  return {
    requestPermission,
    showNotification,
    notifySystem,
    subscribe,
    isSupported: notificationManager.isSupported(),
    permission: notificationManager.getPermission()
  }
}

// Export singleton instance
export const notifications = NotificationManager.getInstance()
export default NotificationManager

// Types
export type { NotificationOptions }
